._manageLayout_ {
    display: flex;
    min-height: 100vh;
    background: var(--main-page-background);
}
._sidebar_ {
    background: var(--gray-900);
    min-width: 14rem;   /* ~224px */
    padding: var(--padding-LG);
    display: flex;
    flex-direction: column;
}
._nav_ {
    display: flex;
    flex-direction: column;
    gap: var(--gap-MD);
    list-style: none;
    padding: 0;
    margin: 0;
}
._navItem_ {
    margin-bottom: var(--gap-SM);
}
._navLink_ {
    color: var(--white);
    text-decoration: none;
    padding: var(--padding-SM) var(--padding-MD);
    border-radius: var(--radius-small);
    display: block;
    font-weight: 500;
    transition: background .2s;
}
._navLink_:hover,
._navLink_.active {
    background: var(--gray-700);
}
.ws-bg-dark    { background: var(--gray-900); }
.ws-text-white { color: var(--white); }
.ws-p-4        { padding: 1rem; }
.ws-pt-8       { padding-top: 2rem; }
.ws-px-8       { padding-left: 2rem; padding-right: 2rem; }
.ws-py-8       { padding-top: 2rem; padding-bottom: 2rem; }
.ws-flex       { display: flex; }
.ws-flex-1     { flex: 1; }
.ws-flex-col   { flex-direction: column; }
.ws-min-h-full { min-height: 100vh; }
.ws-gap-md     { gap: var(--gap-MD); }
.ws-min-w-56   { min-width: 14rem; }
