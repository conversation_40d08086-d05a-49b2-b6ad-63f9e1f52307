:where(._xsmall_14rrm_47) {
    font-size:.75rem;
    line-height:1rem;
    --base-tracking:0em
}

:where(._smallish_14rrm_53) {
    font-size:.875rem;
    line-height:1.125rem
}

:where(._small_14rrm_53) {
    font-size:.875rem;
    line-height:1.25rem
}

:where(._base_14rrm_63) {
    font-size:1rem;
    line-height:1.5rem
}

:where(._large_14rrm_68) {
    font-size:1.125rem;
    line-height:1.75rem
}

:where(._xlarge_14rrm_73) {
    font-size:1.25rem;
    line-height:1.75rem
}

:where(._extralight_14rrm_78) {
    font-weight:200
}

:where(._light_14rrm_82) {
    font-weight:300
}

:where(._normal_14rrm_86) {
    font-weight:400
}

:where(._medium_14rrm_90) {
    font-weight:500
}

:where(._semibold_14rrm_94) {
    font-weight:600;
    --tracking-multiplier:2
}

:where(._bold_14rrm_99) {
    font-weight:700;
    --tracking-multiplier:2
}

:where(._primary_14rrm_104) {
    color:var(--text-primary)
}

:where(._primary_14rrm_104) :where(a) {
    color:var(--link);
    text-decoration:none
}

:where(:where(._primary_14rrm_104) :where(a):hover) {
    text-decoration:underline
}

:where(._secondary_14rrm_115) {
    color:var(--text-secondary)
}

:where(._tertiary_14rrm_119) {
    color:var(--text-tertiary)
}

:where(._quaternary_14rrm_123) {
    color:var(--text-quaternary)
}

:where(._error_14rrm_127) {
    color:var(--platform-error)
}

:where(._inherit_14rrm_131) {
    color:inherit
}

:where(._secondary_14rrm_115,._tertiary_14rrm_119,._quaternary_14rrm_123,._error_14rrm_127) :where(a){
    color:inherit
}

:where(._monospace_14rrm_139) {
    font-family:Menlo,Monaco,Lucida Console,Arial
}


/*
  Root container for the page layout.
  - Uses flex column for vertical stacking.
  - Centers text and stretches children to fit width.
  - Adds responsive padding and full viewport height.
*/
.root-container {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    text-align: center;
    padding: var(--spacing-40) var(--padding-LG) 0 var(--padding-LG);
    margin: auto;
    height: 100dvh;
}

/* Block for title and heading margin */
.title-block {
    margin-bottom: var(--padding-2XL)
}

/* H1 heading, removes default margin */
.heading {
    margin: 0
}

/* Main heading text styling */
.heading-main {
    font-size: var(--spacing-32);
    line-height: var(--spacing-40);
    font-weight: 500;
    --base-tracking: -.02em
}

.heading-subtitle {
    margin-top:var(--padding-MD)
}

.footer-container {
    margin-top: auto;
    margin-bottom: var(--padding-LG)
}

.footer-separator {
    padding:0 var(--padding-SM)
}

.footer-separator:before {
    content:" | "
}

@media screen and (min-width:450px) {
    .root-container {
        --max-inner-width: 21.25rem;
        padding-top: 15vh;
        max-width: calc(var(--max-inner-width) + 2*var(--padding-LG));
        height: auto
    }
    .footer-container {
        margin-top: var(--padding-XL)
    }
}

@media (min-width:800px) {}
@media (max-width:450px) {}

/*
  Removes default fieldset box and layout, but keeps semantic grouping.
  Allows child elements to participate directly in the parent flex/grid layout.
*/
.fieldset-root {
    display: contents;
}

/*
  Root form container.
  - Uses flex column for vertical alignment of form elements.
  - Stretches children to fill container width.
*/
.form-root {
    display: flex;
    flex-direction: column;
    align-items: stretch;
}

/*
  Section container for vertical stacking with spacing between children.
*/
.section {
    display: flex;
    flex-direction: column;
    gap: var(--padding-XL);
}

/*
  Form fields container.
  (Add specific styles as needed)
*/
.fields {}

.fields-group {
    display:flex;
    flex-direction:column;
    align-items:stretch;
    gap:var(--padding-MD)
}

/*
  Container for form call-to-action buttons.
  Adds vertical padding for button area.
*/
.ctas {
    padding: var(--padding-XL) 0;
}

.ctas-group {
    display:flex;
    flex-direction:column;
    align-items:stretch;
    gap:var(--padding-MD)
}


/*
  Group for an input field and label, manages state styling and transitions.
  - Sets up relative positioning.
  - Handles disabled state, focus, and error state.
  - Dynamically updates opacity, pointer events, and input color via CSS variables.
*/
.form-field-group {
    position: relative;
}
.form-field-group:has(:disabled) {
    --cursor: not-allowed;
    opacity: .5;
}
/*
  When input is focused or has errors:
  - Disables pointer events for the absolute label.
  - Hides label background for visual clarity.
  - Moves label text up and scales it (floating label effect).
*/
.form-field-group:has(.target:where([data-focus-within],[data-focused])) .form-label-absolute,
.form-field-group:has(.form-field-errors) .form-label-absolute {
    pointer-events: none;
}
.form-field-group:has(.target:where([data-focus-within],[data-focused])) .form-label-absolute:before,
.form-field-group:has(.form-field-errors) .form-label-absolute:before {
    display: none;
}
.form-field-group:has(.target:where([data-focus-within],[data-focused])) .label-text-positioner,
.form-field-group:has(.form-field-errors) .label-text-positioner {
    /* Animates label up when focused or error */
    transform: translateY(-50%);
}
.form-field-group:has(.target:where([data-focus-within],[data-focused])) .label-text,
.form-field-group:has(.form-field-errors) .label-text {
    /* Shrinks label text when input is focused or in error */
    transform: translate(-12px) scale(.88);
}
/*
  Updates input color when focused or visible, and on error.
  - Primary color on focus, error color on validation error.
*/
.form-field-group:has(:where(.is-typeable .target:where([data-focused],[data-focus-within]):not([readonly])), .target:where([data-focus-visible]:not([readonly]))) {
    --input-state-color: var(--primary);
}
.form-field-group:has(.form-field-errors) {
    --input-state-color: var(--platform-error);
}

/*
  Main wrapper for a single input field.
  - Aligns input and icons horizontally.
  - Applies border, background, and padding.
  - Ensures consistent height and rounded corners.
*/
.form-field {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: stretch;
    height: 3.25rem;
    border-radius: var(--field-border-radius);
    line-height: 1.5rem;
    color: var(--text-primary);
    font-size: 1rem;
    width: 100%;
    border: 1px solid var(--input-state-color, var(--border-medium));
    padding: 0 var(--spacing-20);
}

/*
  Indicates an element is typeable (shows text cursor).
*/
.is-typeable {
    --cursor: text;
    cursor: var(--cursor);
}

.has-value .form-label-absolute {pointer-events:none}
.has-value .form-label-absolute:before,._holdOpen_1xmkn_148 .form-label-absolute:before {display:none}
.has-value .label-text-positioner {transform:translateY(-50%)}
.has-value .label-text {transform:translate(-12px) scale(.88)}

/*
  Absolutely positioned label for form fields (floating/clickable label).
  - Stretches to fill the parent container.
  - Uses a background via :before for floating effect or highlight.
*/
.form-label-absolute {
    cursor: var(--cursor);
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}
.form-label-absolute:before {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    border-radius: var(--field-border-radius);
    background-color: var(--main-page-background);
}

/*
  Positions label text absolutely within input wrapper for floating label effect.
  - Vertically centers label text.
  - Adds horizontal padding and transition for smooth animation.
*/
.label-text-positioner {
    --placeholder-transition: transform .08s ease-in-out;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 0 var(--spacing-20);
    display: flex;
    align-items: center;
    transition: var(--placeholder-transition);
}

/*
  Styles the actual label text for floating labels.
  - Adds background and horizontal padding for visual separation.
  - Smooth transition for animation effects.
  - Sets color, font size, and cursor style.
  - Slight negative translation for alignment.
*/
.label-text {
    --horizontal-padding: 6px;
    background-color: var(--main-page-background);
    padding: 1px var(--horizontal-padding);
    transition: var(--placeholder-transition);
    color: var(--input-state-color, var(--text-tertiary));
    font-size: 1rem;
    line-height: 1;
    cursor: var(--cursor);
    transform: translate(calc(-1*var(--horizontal-padding)));
}


/*
  Styles for the input element inside custom form fields.
  - Removes native background, border, and outline.
  - Inherits parent font and expands to full width.
  - Eliminates padding for tight alignment in custom layout.
  - Hides the placeholder text for custom floating label behavior.
*/
.form-input {
    background-color: transparent;
    outline: none;
    border: 0;
    font: inherit;
    width: 100%;
    padding: 0;
}
.form-input::placeholder {
    opacity: 0;
}

.form-input-end-decoration {display:flex}
.form-input-end-decoration:before {content:"";display:block;width:var(--padding-SM)}

.form-errors {
  padding: 0;
  margin: 0;
}

.form_error:last-of-type {
  margin-bottom: var(--padding-MD);
}
.form_error:first-of-type {
  margin-top: var(--padding-XS);
}
.form_error {
  display: flex;
  align-items: center;
  gap: var(--padding-SM);
  color: var(--platform-error);
  font-size: .75rem;
  line-height: 1.4;
  text-align: left;
}

.forgot-password {text-align:left;padding-left:var(--padding-MD)}


.btn{-webkit-appearance:none;-moz-appearance:none;appearance:none;display:flex;justify-content:center;align-items:center;height:3.25rem;padding:0 var(--padding-XL);border:none;border-radius:var(--field-border-radius);cursor:pointer;transition:background-color .1s ease;font-size:1rem;line-height:1.5rem;text-align:center;font-family:inherit;font-weight:400;text-decoration:none}
.btn:active{opacity:.8}
.btn:focus-visible{outline:0;box-shadow:#fff 0 0 0 2px,var(--button-focus-ring-color)0 0 0 4px,#0000 0 0}
.btn:disabled,.btn._focusableDisabled_625o4_77{cursor:not-allowed;opacity:.5}

.btn-left-align{justify-content:start}
.btn-primary{background-color:var(--button-primary-background);color:var(--button-text-on-primary)}
.btn-primary:not(:disabled,._focusableDisabled_625o4_77):hover{background-color:var(--button-primary-hover)}
.btn_outline{background-color:transparent;color:var(--button-text-on-base);border:1px solid var(--border-medium)}
.btn_outline:not(:disabled,._focusableDisabled_625o4_77):hover{background-color:var(--button-outline-hover)}

.btn-transparent{
    background-color:transparent;
    color:var(--button-text-on-base);
    height:auto;
    padding:0;
    border-radius:0;
    width:fit-content;
    margin:auto;
    margin-top:var(--padding-SM)
}
.btn_decoration{display:flex;align-items:center}
.btn-left-align .btn_decoration{margin-right:var(--padding-LG)}
.btn-logo-positioner{height:16px;width:16px;display:grid;place-items:center}
.btn-logo-positioner>*{position:absolute}
.btn-img-root{display:block}
.btn-apple-left-aligned{translate:0-1px}

.btn-toggle-visibility {isolation:isolate;display:flex;align-items:center;justify-content:center;cursor:pointer;position:relative}
.btn-toggle-visibility:before{z-index:-1;content:"";display:block;position:absolute;inset:calc(var(--padding-SM)*-1);border-radius:var(--radius-circle)}
.btn-toggle-visibility:where(:hover,:focus-visible):before{background-color:#ececec}
.btn-toggle-visibility:active{opacity:.8}

:where(.btn-icon){-webkit-appearance:none;-moz-appearance:none;appearance:none;border:none;background:none;padding:0;font:inherit;color:inherit}
:where(.btn-icon):focus-visible{outline:none}

.oauth-root {display:flex;flex-direction:column;align-items:stretch;gap:var(--padding-MD)}

.section-delimiter-root {display:grid;grid-template-columns:1fr max-content 1fr;align-items:center}
.section-delimiter-line {height:1px;background-color:var(--gray-300)}
.section-delimiter-name {margin:0 var(--padding-LG);text-transform:uppercase;font-size:13px;font-weight:510}




.password-requirements {
    background: #f4f6fa;
    border: 1px solid var(--border-xheavy, #e0e0e0);
    border-radius: 8px;
    padding: 1.25em 1em;
    text-align: left;
    font-family: 'Inter', Arial, sans-serif;
    font-size: .875rem;
    font-weight: 400;
    line-height: 1.25rem;
    margin-bottom: 1em;
}

.password-requirements__title {
    display: block;
    margin-bottom: 0.5em;
    font-size: 1.06em;
    font-weight: 600;
    color: #222;
}

.password-requirements .requirements {
    margin: 0;
    margin-top: 0.5em;
    padding: 0;
    list-style: none;
}

.password-requirements .requirements li {
    display: flex;
    align-items: center;
    color: #111;
    margin-bottom: 0.15em;
    font-size: 1em;
    position: relative;
    padding-left: 1.7em;
}
.password-requirements .requirements li:before {
    content: "•";
    color: #888;
    position: absolute;
    left: 0;
    font-size: 1.2em;
    line-height: 1;
}

/* zxcvbn Password Feedback */
.zxcvbn-feedback {
    margin-top: 0.7em;
    color: #222;
}

.zxcvbn-feedback .very-weak-password   { color: #d32f2f; }
.zxcvbn-feedback .weak-password        { color: #e57373; }
.zxcvbn-feedback .fair-password        { color: #c87100; }
.zxcvbn-feedback .strong-password      { color: #388e3c; }
.zxcvbn-feedback .very-strong-password { color: #1976d2; }


.zxcvbn-feedback .warning {
    color: #c87100;
    margin-bottom: 0.3em;
}

.zxcvbn-feedback .suggestions {
    padding: 0;
    color: #1976d2;
}
.zxcvbn-feedback .suggestions li {
    display: flex;
    align-items: center;
    color: #111;
    margin-bottom: 0.15em;
    font-size: 1em;
    position: relative;
    padding-left: 1.7em;
}
.zxcvbn-feedback .suggestions li:before {
    content: "•";
    color: #888;
    position: absolute;
    left: 0;
    font-size: 1.2em;
    line-height: 1;
}
