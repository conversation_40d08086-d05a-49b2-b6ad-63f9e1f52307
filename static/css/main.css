/*
  Project-wide theme variables for the Wheel Size UI.
  These CSS custom properties define the core color palette,
  backgrounds, button states, borders, spacing, radii, and typography
  used throughout the application. The `.wheel-size-theme` class
  provides scoped theme variables for branded pages, while `:root`
  defines the base design system and utility values.
*/

.wheel-size-theme {
    --main-page-background:var(--openai-dot-com-background);  /* Background color for main page area */
    --selection:var(--selection-blue);                        /* Color for selection highlights */
    --primary:var(--accent-blue);                             /* Primary accent color (e.g., for buttons/links) */
    --link:var(--accent-blue);                                /* Color for standard links */
    --field-border-radius:var(--radius-circle);               /* Border radius for input fields */
    --button-primary-background:#131313;                      /* Main background color for primary buttons */
    --button-primary-hover:#333333;                           /* Background color for primary buttons on hover */
    --button-outline-hover:#ececec;                           /* Background for outline buttons on hover */
    --button-text-on-primary:#ffffff;                         /* Text color for primary buttons */
    --button-focus-ring-color:rgb(155,155,155);               /* Color of focus ring for buttons */
}

:root {
    --white:#fff;                                             /* White color */
    --black:#000;                                             /* Black color */
    --pretty-dark-gray:#202123;                               /* Dark gray for backgrounds or surfaces */
    --gray-50:#f9f9f9;                                        /* Light gray (backgrounds, surfaces) */
    --gray-100:#ececec;                                       /* Slightly darker light gray */
    --gray-200:#e3e3e3;                                       /* Light gray for borders or surfaces */
    --gray-300:#cdcdcd;                                       /* Medium-light gray */
    --gray-400:#b4b4b4;                                       /* Medium gray */
    --gray-500:#9b9b9b;                                       /* Slightly dark gray */
    --gray-600:#676767;                                       /* Darker gray */
    --gray-700:#424242;                                       /* Even darker gray */
    --gray-750:#2f2f2f;                                       /* Very dark gray (near-black) */
    --gray-800:#212121;                                       /* Near-black gray */
    --gray-900:#171717;                                       /* Almost black */
    --gray-950:#0d0d0d;                                       /* Deepest black */
    --red-500:#ef4444;                                        /* Main error or danger red */
    --red-700:#b91c1c;                                        /* Darker red for error states */
    --brand-purple:#ab68ff;                                   /* Brand accent purple */
    --openai-dot-com-background:#f9f9f9;                      /* Main background for OpenAI theme */
    --platform-green:#10a37f;                                 /* Platform accent green (success, etc.) */
    --platform-error:#d00e17;                                 /* Platform-specific error color */
    --accent-blue:#3e68ff;                                    /* Main accent blue (primary links, actions) */
    --scrim:rgba(0,0,0,.4);                                  /* Overlay for modals or dialogs */
    --dialog-shadow:0px 20px 25px -5px rgba(0,0,0,.1),0px 8px 10px -6px rgba(0,0,0,.1);  /* Main shadow for dialogs */
    --popover-shadow:0px 8px 32px 0px rgba(0,0,0,.2);         /* Shadow for popovers or dropdowns */
    --menu-primary-surface-item-hover:rgba(0,0,0,.05);        /* Background for menu item hover */
    --menu-primary-surface-item-press:rgba(0,0,0,.1);         /* Background for menu item active/pressed */
    --icon-default-tertiary:#a4a4a4;                          /* Tertiary icon color (muted, disabled) */
    --text-primary:var(--gray-950);                           /* Main text color */
    --text-secondary:#5d5d5d;                                 /* Secondary text color (less emphasis) */
    --text-tertiary:var(--gray-400);                          /* Tertiary text color (placeholders, etc.) */
    --text-quaternary:var(--gray-300);                        /* Quaternary text (very subtle, e.g., dividers) */
    --text-selection:var(--white);                            /* Text color on selected backgrounds */
    --text-error:#f93a37;                                     /* Text color for error messages */
    --surface-error:249 58 55;                                /* RGB for surfaces in error states */
    --border-xlight:rgb(0 0 0/5%);                            /* Extra-light border */
    --border-light:rgb(0 0 0/10%);                            /* Light border */
    --border-medium:rgb(0 0 0/15%);                           /* Medium border */
    --border-heavy:rgb(0 0 0/20%);                            /* Heavy border */
    --border-xheavy:rgb(0 0 0/25%);                           /* Extra-heavy border */
    --border-sharp:rgb(0 0 0/5%);                             /* Sharp border, similar to xlight */
    --border-white-10:rgb(255 255 255/10%);                   /* Light white border (on dark) */
    --main-surface-primary:var(--white);                      /* Main surface (background) color */
    --main-surface-secondary:var(--gray-50);                  /* Secondary surface color */
    --main-surface-tertiary:var(--gray-100);                  /* Tertiary surface color */
    --sidebar-surface-primary:var(--gray-50);                 /* Primary sidebar background */
    --sidebar-surface-secondary:var(--gray-100);              /* Secondary sidebar background */
    --sidebar-surface-tertiary:var(--gray-200);               /* Tertiary sidebar background */
    --selection-blue:#007aff;                                 /* iOS blue for selections and highlights */
    --promo-surface:#edf6ff;                                  /* Surface for promo banners */
    --promo-primary:#0285ff;                                  /* Primary color for promos */
    --promo-border:rgba(2,133,255,.1);                        /* Border for promo banners */
    --main-page-background:var(--main-surface-primary);       /* Main page background (linked to primary surface) */
    --selection:var(--selection-blue);                        /* Selection color (linked to selection-blue) */
    --link:var(--accent-blue);                                /* Link color (linked to accent blue) */
}

:root {
    --padding-2XL:2rem;       /* Extra-extra-large padding */
    --padding-XL:1.5rem;      /* Extra-large padding */
    --padding-LG:1rem;        /* Large padding */
    --padding-MD:.75rem;      /* Medium padding */
    --padding-SM:.5rem;       /* Small padding */
    --padding-XS:.25rem;      /* Extra-small padding */
    --gap-2XL:2rem;           /* Extra-extra-large gap */
    --gap-XL:1.5rem;          /* Extra-large gap */
    --gap-LG:1rem;            /* Large gap */
    --gap-MD:.75rem;          /* Medium gap */
    --gap-SM:.5rem;           /* Small gap */
    --gap-XS:.25rem;          /* Extra-small gap */
    --spacing-2:.125rem;      /* 2px spacing (for margins, padding, etc.) */
    --spacing-4:.25rem;       /* 4px spacing */
    --spacing-6:.375rem;      /* 6px spacing */
    --spacing-8:.5rem;        /* 8px spacing */
    --spacing-10:.625rem;     /* 10px spacing */
    --spacing-12:.75rem;      /* 12px spacing */
    --spacing-14:.875rem;     /* 14px spacing */
    --spacing-16:1rem;        /* 16px spacing (1rem) */
    --spacing-18:1.125rem;    /* 18px spacing */
    --spacing-20:1.25rem;     /* 20px spacing */
    --spacing-24:1.5rem;      /* 24px spacing */
    --spacing-28:1.75rem;     /* 28px spacing */
    --spacing-32:2rem;        /* 32px spacing */
    --spacing-36:2.25rem;     /* 36px spacing */
    --spacing-40:2.5rem;      /* 40px spacing */
    --spacing-44:2.75rem;     /* 44px spacing */
    --spacing-48:3rem;        /* 48px spacing */
    --spacing-56:3.5rem;      /* 56px spacing */
    --spacing-64:4rem;        /* 64px spacing */
    --spacing-72:4.5rem;      /* 72px spacing */
    --spacing-80:5rem;        /* 80px spacing */
    --spacing-96:6rem;        /* 96px spacing */
    --spacing-112:7rem;       /* 112px spacing */
    --spacing-128:8rem;       /* 128px spacing */
    --spacing-144:9rem;       /* 144px spacing */
    --radius-xxsmall:.1875rem;/* Extra-extra-small border radius */
    --radius-xsmall:.375rem;  /* Extra-small border radius */
    --radius-small:.5rem;     /* Small border radius */
    --radius-medium:.75rem;   /* Medium border radius */
    --radius-large:1rem;      /* Large border radius */
    --radius-circle:99999px;  /* Fully rounded (circle) border radius */
}

:root {
    --elevation-1:0px 4px 16px 0px rgba(0,0,0,.05);  /* Low elevation shadow for elements */
}

:root {
    --base-tracking:-.01em;           /* Base letter spacing (tracking) */
    --tracking-multiplier:1;          /* Multiplier for letter spacing */
}

* {
    box-sizing:border-box;                    /* Use border-box sizing for all elements */
    -webkit-tap-highlight-color:transparent;  /* Remove tap highlight on mobile */
    font-family:OpenAI Sans,SF Pro,-apple-system,BlinkMacSystemFont,Helvetica,sans-serif; /* Font stack for UI */
    text-rendering:optimizeLegibility;        /* Optimize text rendering for legibility */
    font-synthesis:none;                      /* Prevent font synthesis (italics/bold) by browser */
    -webkit-font-smoothing:antialiased;       /* Smooth font rendering on Webkit */
    -moz-osx-font-smoothing:grayscale;        /* Smooth font rendering on macOS */
    letter-spacing:calc(var(--base-tracking)*var(--tracking-multiplier)); /* Dynamic letter spacing */
}

body {
    margin:0;                                      /* Remove default body margin */
    background-color:var(--main-page-background);  /* Set background from theme variable */
}

* {
    --scrollbar-thumb-color:var(--main-surface-tertiary);     /* Set default scrollbar color */
    scrollbar-color:var(--scrollbar-thumb-color) transparent; /* Use variable color for scrollbars */
}

:hover {
    --scrollbar-thumb-color:var(--gray-200);  /* On hover, change scrollbar thumb color */
}
