services:
  ws-auth-api:
    image: ws-auth-api-image
    build:
      context: .
      dockerfile: docker/Dockerfile
    container_name: ws-auth-api-django-app
    restart: always
    networks:
        - shared_network
    volumes:
      - .:/var/task
#    ports:
#        - "80:8000"
    expose:
        - "8000"
    environment:

      DJANGO_SETTINGS_MODULE: config.settings.dev_evgeniy
      DJANGO_SECRET_KEY: django-insecure-wxm2eo0w+^5z@w^isang8b%u0zoy19=!tz7ol!-c=1$gn!h5tk

      REDIS_URL: redis://redis:6379/0
    entrypoint: ["/bin/bash", "-c"]
#    command: ["tail -f /dev/null"]  # keep the container running for initial setup or manual debugging
#    command: [
#      "poetry install --no-interaction --no-ansi && \
#       poetry run gunicorn config.wsgi \
#       --bind 0.0.0.0:8000 \
#       --reload \
#       --reload-engine poll \
#       --workers 1 \
#       --log-level=debug"
#    ]
    command: [
      "poetry install --no-interaction --no-ansi && \
       poetry run python manage.py runserver 0.0.0.0:8000"
    ]

networks:
  shared_network:
    external: true
