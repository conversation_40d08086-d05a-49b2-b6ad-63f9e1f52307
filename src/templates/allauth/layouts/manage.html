{% extends "allauth/layouts/base.html" %}

{% load i18n static %}
{% load allauth %}

{% block extra_head %}
<link rel="stylesheet" href="{% static 'css/manage.css' %}">
{% endblock extra_head %}

{% block body %}
<!-- Main container -->
<div class="_root_1k32w_47">
    <div class="_manageLayout_ ws-flex ws-min-h-full">
        <aside class="_sidebar_ ws-bg-dark ws-p-4 ws-min-w-56">
            <ul class="_nav_ ws-flex-col ws-gap-md">
                {% url 'account_email' as email_url_ %}
                {% if email_url_ %}
                    <li class="_navItem_">
                        <a class="{% block nav_class_email %}_navLink_ ws-text-white{% endblock %}"
                           href="{{ email_url_ }}">Change Email</a>
                    </li>
                {% endif %}

                {% url 'account_change_password' as change_password_url_ %}
                {% if change_password_url_ %}
                    <li class="_navItem_">
                        <a class="{% block nav_class_password %}_navLink_ ws-text-white{% endblock %}"
                           href="{{ change_password_url_ }}">Change Password</a>
                    </li>
                {% endif %}

                {% url 'account_change_phone' as phone_url_ %}
                {% if phone_url_ %}
                    <li class="_navItem_">
                        <a class="{% block nav_class_phone %}_navLink_ ws-text-white{% endblock %}"
                           href="{{ phone_url_ }}">Change Phone</a>
                    </li>
                {% endif %}

                {% url 'socialaccount_connections' as connections_url_ %}
                {% if connections_url_ %}
                    <li class="_navItem_">
                        <a class="{% block nav_class_socialaccount %}_navLink_ ws-text-white{% endblock %}"
                           href="{{ connections_url_ }}">Third-Party Accounts</a>
                    </li>
                {% endif %}

                {% url 'mfa_index' as mfa_index_url_ %}
                {% if mfa_index_url_ %}
                    <li class="_navItem_">
                        <a class="{% block nav_class_mfa %}_navLink_ ws-text-white{% endblock %}"
                           href="{{ mfa_index_url_ }}">Two-Factor Authentication</a>
                    </li>
                {% endif %}

                {% url 'usersessions_list' as usersessions_list_url_ %}
                {% if usersessions_list_url_ %}
                    <li class="_navItem_">
                        <a class="{% block nav_class_usersessions %}_navLink_ ws-text-white{% endblock %}"
                           href="{{ usersessions_list_url_ }}">Sessions</a>
                    </li>
                {% endif %}
            </ul>
        </aside>
        <main class="_main_ ws-flex-1 ws-px-8 ws-py-8">
            <div id="content">
                {% if messages %}
                    <div class="ws-pt-8">
                        {% for message in messages %}
                            {% element alert level=message.tags %}
                                {% slot message %}
                                    {{ message }}
                                {% endslot %}
                            {% endelement %}
                        {% endfor %}
                    </div>
                {% endif %}
                {% block content %}{% endblock %}
            </div>
        </main>
    </div>
</div>
{% endblock %}
