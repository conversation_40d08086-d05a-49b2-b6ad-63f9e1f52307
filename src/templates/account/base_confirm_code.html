{% extends "account/base_entrance.html" %}

{% load i18n %}
{% load allauth account %}


{% block content %}

    {% setvar title_ %}
        {% block title %}{% endblock %}
    {% endsetvar %}

    {% setvar action_url %}
        {% block action_url %}{% endblock %}
    {% endsetvar %}

    {% setvar extra_tags %}
        {% block extra_tags %}{% endblock %}
    {% endsetvar %}

    {% setvar form_tags %}
        entrance,{{ extra_tags }}
    {% endsetvar %}

    {% setvar submit_button_tags %}
        confirm,{{ extra_tags }}
    {% endsetvar %}

    {% setvar recipient %}
        {% block recipient %}{% endblock %}
    {% endsetvar %}

    <div class="title-block">
        <h1 class="heading">
            <span class="heading-main">{{ title_ }}</span>
        </h1>
        <div class="heading-subtitle">
            <span class="_base_14rrm_63 _normal_14rrm_86 _secondary_14rrm_115">
                We’ve sent a verification code to {{ recipient }}.<br>
                It will expire soon, so please enter it promptly.
            </span>
        </div>
    </div>


    <fieldset class="fieldset-root">

        <form method="post"
              action="{{ action_url }}"
              id=":verify_form:"
              class="form-root"
              autocomplete="on"
              novalidate>

            <div class="section fields">

                {% csrf_token %}

                {{ redirect_field }}

                <div class="form-field-group">

                    <div class="form-field is-typeable">
                        <label class="form-label-absolute" for=":verify_form:code">
                            <span class="label-text-positioner">
                                <span class="label-text">Code</span>
                            </span>
                        </label>
                        <input id=":verify_form:code"
                               name="code"
                               type="text"
                               class="form-input target"
                               autocomplete="one-time-code"
                               required
                               maxlength="6"
                               placeholder="Code">
                    </div>
                    <span aria-live="polite" aria-atomic="true">
                    {% if form.code.errors %}
                        <span slot="errorMessage">
                            <ul class="form-errors">
                                {% for error in form.code.errors %}
                                <li class="form_error">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" title="Error">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M8 14.667A6.667 6.667 0 1 0 8 1.333a6.667 6.667 0 0 0 0 13.334z" fill="#D00E17" stroke="#D00E17" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M8 4.583a.75.75 0 0 1 .75.75V8a.75.75 0 0 1-1.5 0V5.333a.75.75 0 0 1 .75-.75z" fill="#fff"></path>
                                        <path d="M8.667 10.667a.667.667 0 1 1-1.334 0 .667.667 0 0 1 1.334 0z" fill="#fff"></path>
                                    </svg>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        </span>
                    {% endif %}
                    </span>
                </div>

            </div>

            <div class="section ctas">
                <!-- Call-to-action buttons go here -->
                <div class="ctas-group">
                    <button class="btn btn-primary"
                            type="submit"
                            name="intent"
                            value="email">Continue</button>

                    {% if can_resend %}
                        <button class="btn btn-transparent"
                                type="submit"
                                form="resend"
                                name="intent"
                                value="generate">Resend email</button>
                    {% endif %}

                    {% if cancel_url %}
                        {% setvar link %}<a href="{{ cancel_url }}">{% endsetvar %}
                        {% setvar end_link %}</a>{% endsetvar %}

                        <span class="_base_14rrm_63 _normal_14rrm_86 _primary_14rrm_104">
                            {% blocktranslate %}{{ link }}Cancel{{ end_link }}{% endblocktranslate %}
                        </span>
                    {% else %}
                        <button class="btn btn-transparent"
                                type="submit"
                                form="logout-from-stage"
                                name="intent"
                                value="generate">Cancel</button>
                    {% endif %}
                </div>
            </div>
        </form>

        <form id="resend"
              method="post"
              action="{{ action_url }}">

            <input type="hidden" name="action" value="resend">

            {% csrf_token %}
            {{ redirect_field }}

        </form>

        {% if not cancel_url %}
            <form id="logout-from-stage"
                  method="post"
                  action="{% url 'account_logout' %}">

                <input type="hidden" name="next" value="{% url 'account_login' %}">

                {% csrf_token %}

            </form>
        {% endif %}

    </fieldset>

    {% setvar summary_ %}
        {% block change_title %}{% endblock %}
    {% endsetvar %}

    {% if can_change %}
        {% element details open=change_form.errors %}
            {% slot summary %}
                {{ summary_ }}
            {% endslot %}
            {% slot body %}
                {% element form form=change_form method="post" action=action_url %}
                    {% slot body %}
                        {% csrf_token %}
                        {% element fields form=change_form unlabeled=True %}
                        {% endelement %}
                        {{ redirect_field }}
                    {% endslot %}
                    {% slot actions %}
                        {% element button name="action" value="change" type="submit" %}
                            Change
                        {% endelement %}
                    {% endslot %}
                {% endelement %}
            {% endslot %}
        {% endelement %}
    {% endif %}
{% endblock content %}


{% block extra_body %}

<script type="text/javascript">
document.addEventListener('DOMContentLoaded', function () {
    const form = document.querySelector('.form-root');
    const codeInput = form.querySelector('input[name="code"]');

    // Floating label data-focused logic
    codeInput.addEventListener('focus', function() {
        codeInput.setAttribute('data-focused', 'true');
    });
    codeInput.addEventListener('blur', function() {
        if (!codeInput.value) {
            codeInput.removeAttribute('data-focused');
        }
    });
    if (codeInput.value) {
        codeInput.setAttribute('data-focused', 'true');
    }

});
</script>

{% endblock extra_body %}
