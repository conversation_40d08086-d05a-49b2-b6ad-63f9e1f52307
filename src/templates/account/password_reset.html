{% extends "account/base_entrance.html" %}

{% load i18n allauth account %}

{% block head_title %}
    {% trans "Password Reset" %}
{% endblock head_title %}

{% block extra_head %}

{{ block.super }}

<!-- Canonical URL for SEO -->
<link rel="canonical" href="https://auth.wheel-size.com/password/reset/">

{% endblock extra_head %}

{% block content %}

    <div class="title-block">
        <h1 class="heading">
            <span class="heading-main">{% trans "Password Reset" %}</span>
        </h1>

        <div class="heading-subtitle">
            <span class="_base_14rrm_63 _normal_14rrm_86 _secondary_14rrm_115">
                {% trans "Forgot your password? Enter your email below and we’ll send you a link to reset it." %}
            </span>
        </div>
    </div>

    {% if user.is_authenticated %}
        {% include "account/snippets/already_logged_in.html" %}
    {% endif %}

    <fieldset class="fieldset-root">

        {% url 'account_reset_password' as reset_url %}

        <form method="post"
              action="{{ reset_url }}"
              id=":reset:"
              class="form-root"
              autocomplete="on"
              novalidate>

            <div class="section fields">

                {% csrf_token %}

                {{ redirect_field }}

                <div class="fields-group">

                    <div class="form-field-group">

                        <div class="form-field is-typeable">

                            <label class="form-label-absolute" for=":reset:email">
                                <span class="label-text-positioner">
                                    <span class="label-text">Email address</span>
                                </span>
                            </label>

                            <input id=":reset:email"
                                   name="email"
                                   type="email"
                                   class="form-input target"
                                   placeholder="Email address"
                                   autocomplete="email"
                                   maxlength="320"
                                   required="">

                        </div>

                        <span aria-live="polite" aria-atomic="true">
                        {% if form.email.errors %}
                            <span slot="errorMessage">
                                <ul class="form-errors">
                                    {% for error in form.email.errors %}
                                    <li class="form_error">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" title="Error">
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M8 14.667A6.667 6.667 0 1 0 8 1.333a6.667 6.667 0 0 0 0 13.334z" fill="#D00E17" stroke="#D00E17" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M8 4.583a.75.75 0 0 1 .75.75V8a.75.75 0 0 1-1.5 0V5.333a.75.75 0 0 1 .75-.75z" fill="#fff"></path>
                                            <path d="M8.667 10.667a.667.667 0 1 1-1.334 0 .667.667 0 0 1 1.334 0z" fill="#fff"></path>
                                        </svg>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            </span>
                        {% endif %}
                        </span>
                    </div>

                </div>

            </div>


            <div class="section ctas">
                <!-- Call-to-action buttons go here -->

                <button class="btn btn-primary"
                        type="submit"
                        name="intent"
                        value="email">Reset My Password</button>

                <span class="_base_14rrm_63 _normal_14rrm_86 _primary_14rrm_104">
                    {% blocktrans %}Please contact us if you have any trouble resetting your password.{% endblocktrans %}
                </span>

            </div>

        </form>

    </fieldset>


{% endblock content %}


{% block extra_body %}

<script type="text/javascript">
document.addEventListener('DOMContentLoaded', function () {
    const form = document.querySelector('.form-root');
    const emailInput = form.querySelector('input[name="email"]');
    const params = new URLSearchParams(window.location.search);

    // Auto-focus email field if email= is in URL
    if (params.has('email') && emailInput) {
        emailInput.focus();
        emailInput.setAttribute('data-focused', 'true');
    }

    // Floating label data-focused logic
    emailInput.addEventListener('focus', function() {
        emailInput.setAttribute('data-focused', 'true');
    });
    emailInput.addEventListener('blur', function() {
        if (!emailInput.value) {
            emailInput.removeAttribute('data-focused');
        }
    });
    if (emailInput.value) {
        emailInput.setAttribute('data-focused', 'true');
    }

});
</script>

{% endblock extra_body %}
