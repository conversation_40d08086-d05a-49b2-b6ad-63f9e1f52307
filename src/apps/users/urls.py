from django.urls import include, path
from django.views.generic.base import TemplateView

from src.apps.users import views


urlpatterns = [
    path("signup/", views.signup_email_step, name="custom_signup_email"),
    path("signup/password/", views.signup_password_step, name="custom_signup_password"),

    path("login/", views.login_email_step, name="custom_login_email"),
    path("login/password/", views.login_password_step, name="custom_login_password"),

    path("", include("allauth.urls")),
    # path("", include("allauth.idp.urls")),

    path("", TemplateView.as_view(template_name="index.html")),

    path("profile/", views.profile, name="profile"),
]
