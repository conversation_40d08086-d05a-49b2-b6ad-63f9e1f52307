from django import forms

from allauth.account.adapter import get_adapter
from allauth.account.fields import <PERSON><PERSON><PERSON><PERSON>
from allauth.account.internal import flows


class SignupForm(forms.Form):
    """
    Signup form for a 2-step registration flow (email-only first step).

    Features:
    - Validates that the provided email is unique.
    - Exposes a flag if an account with this email already exists.
    - Implements custom save logic to prevent account enumeration attacks.
    """
    email = EmailField(label='Email', required=True)

    def __init__(self, *args, **kwargs):
        """
        Initialize the form and set the account existence flag to False.
        """
        self.account_already_exists = False
        super().__init__(*args, **kwargs)

    def clean_email(self):
        """
        Clean and validate the email field:
        - Converts to lowercase.
        - Applies adapter-level cleaning.
        - Ensures email is unique, sets flag if not.

        Returns:
            str: The cleaned and validated email address.
        """
        value = self.cleaned_data["email"].lower()
        value = get_adapter().clean_email(value)
        if value:
            value = self.validate_unique_email(value)
        return value

    def validate_unique_email(self, value) -> str:
        """
        Checks if the email already exists in the system.

        Args:
            value (str): The email address to check.

        Returns:
            str: The processed email address.
        """
        email, self.account_already_exists = flows.manage_email.email_already_exists(value)
        return email

    def try_save(self, request):
        """
        Attempts to save the user.

        If the account already exists:
            - Does not create a new account.
            - Sends an email to inform the user of the existing account.
            - Returns a response indicating that email verification has been sent.

        Returns:
            Response or None: The result of the enumeration prevention flow,
            or None if account does not already exist.
        """
        if self.account_already_exists:
            # Don't create a new account, only send an email informing the user
            # that (s)he already has one...
            email = self.cleaned_data.get("email")
            return flows.signup.prevent_enumeration(request, email=email, phone=None)

        return None


class LoginForm(forms.Form):
    login = EmailField(label='Email')

    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop("request", None)
        super().__init__(*args, **kwargs)

    def clean_login(self):
        value = self.cleaned_data["login"].lower()
        value = get_adapter().clean_email(value)
        return value
