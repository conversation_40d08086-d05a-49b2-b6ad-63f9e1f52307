{% extends "account/base_entrance.html" %}

{% load allauth socialaccount i18n static %}

{% block head_title %}{% trans "Login" %}{% endblock head_title %}

{% block extra_head %}

{{ block.super }}

<!-- Canonical URL for SEO -->
<link rel="canonical" href="https://auth.wheel-size.com/login/password/">

{% endblock extra_head %}


{% block content %}

    <div class="title-block">
        <h1 class="heading">
            <span class="heading-main">{% trans "Enter your password" %}</span>
        </h1>
    </div>

    <fieldset class="fieldset-root">

        {% url 'custom_login_email' as entrance_url %}
        {% url 'custom_login_password' as action_url %}
        {% url 'account_reset_password' as reset_password_url %}

        <form method="post"
              action="{{ action_url }}"
              id=":login:"
              class="form-root"
              autocomplete="on"
              novalidate>

            <div class="section fields">

                {% csrf_token %}

                {{ redirect_field }}

                <input type="hidden" name="remember" id="id_remember" value="1">

                <div class="fields-group">

                    <div class="form-field-group">

                        <div class="form-field is-typeable has-value">
                            <label class="form-label-absolute" for=":login:login">
                                <span class="label-text-positioner">
                                    <span class="label-text">Email address</span>
                                </span>
                            </label>

                            <input id=":login:login"
                                   name="login"
                                   type="text"
                                   class="form-input target"
                                   value="{{ form.login.value|default_if_none:'' }}"
                                   placeholder="Email address"
                                   readonly>

                            <div class="form-input-end-decoration">
                                <div style="white-space:nowrap">
                                    <span class="_base_14rrm_63 _normal_14rrm_86 _primary_14rrm_104">
                                        <a title="Edit email" href="{{ entrance_url }}?email={{ form.login.value }}" data-discover="true">Edit</a>
                                    </span>
                                </div>
                            </div>

                        </div>
                        <span aria-live="polite" aria-atomic="true">
                        {% if form.email.errors %}
                            <span slot="errorMessage">
                                <ul class="form-errors">
                                    {% for error in form.email.errors %}
                                    <li class="form_error">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" title="Error">
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M8 14.667A6.667 6.667 0 1 0 8 1.333a6.667 6.667 0 0 0 0 13.334z" fill="#D00E17" stroke="#D00E17" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M8 4.583a.75.75 0 0 1 .75.75V8a.75.75 0 0 1-1.5 0V5.333a.75.75 0 0 1 .75-.75z" fill="#fff"></path>
                                            <path d="M8.667 10.667a.667.667 0 1 1-1.334 0 .667.667 0 0 1 1.334 0z" fill="#fff"></path>
                                        </svg>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            </span>
                        {% endif %}
                        </span>
                    </div>


                    <div class="form-field-group">
                        <div class="form-field is-typeable">
                            <label class="form-label-absolute" for=":login:password">
                                <div class="label-text-positioner">
                                    <div class="label-text">Password</div>
                                </div>
                            </label>
                            <input id=":login:password"
                                   name="password"
                                   type="password"
                                   class="form-input target"
                                   placeholder="Password"
                                   autocomplete="current-password"
                                   spellcheck="false">

                            <div class="form-input-end-decoration">
                                <button type="button"
                                        class="btn-toggle-visibility btn-icon"
                                        title="Show password">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M5.91444 7.59106C4.3419 9.04124 3.28865 10.7415 2.77052 11.6971C2.66585 11.8902 2.66585 12.1098 2.77052 12.3029C3.28865 13.2585 4.3419 14.9588 5.91444 16.4089C7.48195 17.8545 9.50572 19 12 19C14.4943 19 16.518 17.8545 18.0855 16.4089C19.6581 14.9588 20.7113 13.2585 21.2295 12.3029C21.3341 12.1098 21.3341 11.8902 21.2295 11.6971C20.7113 10.7415 19.6581 9.04124 18.0855 7.59105C16.518 6.1455 14.4943 5 12 5C9.50572 5 7.48195 6.1455 5.91444 7.59106ZM4.55857 6.1208C6.36059 4.45899 8.84581 3 12 3C15.1542 3 17.6394 4.45899 19.4414 6.1208C21.2384 7.77798 22.4152 9.68799 22.9877 10.7438C23.4147 11.5315 23.4147 12.4685 22.9877 13.2562C22.4152 14.312 21.2384 16.222 19.4414 17.8792C17.6394 19.541 15.1542 21 12 21C8.84581 21 6.36059 19.541 4.55857 17.8792C2.76159 16.222 1.58478 14.312 1.01232 13.2562C0.58525 12.4685 0.585249 11.5315 1.01232 10.7438C1.58478 9.688 2.76159 7.77798 4.55857 6.1208ZM12 9.5C10.6193 9.5 9.49999 10.6193 9.49999 12C9.49999 13.3807 10.6193 14.5 12 14.5C13.3807 14.5 14.5 13.3807 14.5 12C14.5 10.6193 13.3807 9.5 12 9.5ZM7.49999 12C7.49999 9.51472 9.51471 7.5 12 7.5C14.4853 7.5 16.5 9.51472 16.5 12C16.5 14.4853 14.4853 16.5 12 16.5C9.51471 16.5 7.49999 14.4853 7.49999 12Z" fill="currentColor"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <span aria-live="polite" aria-atomic="true">
                        {% if form.password.errors %}
                            <span slot="errorMessage">
                                <ul class="form-errors">
                                    {% for error in form.password.errors %}
                                    <li class="form_error">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" title="Error">
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M8 14.667A6.667 6.667 0 1 0 8 1.333a6.667 6.667 0 0 0 0 13.334z" fill="#D00E17" stroke="#D00E17" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M8 4.583a.75.75 0 0 1 .75.75V8a.75.75 0 0 1-1.5 0V5.333a.75.75 0 0 1 .75-.75z" fill="#fff"></path>
                                            <path d="M8.667 10.667a.667.667 0 1 1-1.334 0 .667.667 0 0 1 1.334 0z" fill="#fff"></path>
                                        </svg>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            </span>
                        {% endif %}
                        </span>
                    </div>

                    <span class="forgot-password _small_14rrm_53 _normal_14rrm_86 _primary_14rrm_104">
                        <a href="{{ reset_password_url }}">Forgot password?</a>
                    </span>
                </div>
            </div>

            <div class="section ctas">
                <!-- Call-to-action buttons go here -->

                <button class="btn btn-primary"
                        type="submit"
                        name="intent"
                        value="email">Continue</button>

            </div>

        </form>

    </fieldset>



<!--            <p>-->
<!--                <label for="id_password">Password:</label>-->
<!--                <input type="password" name="password" placeholder="Password" autocomplete="current-password" required="" aria-describedby="id_password_helptext" id="id_password">-->

<!--                <span class="helptext" id="id_password_helptext"><a href="/password/reset/">Forgot your password?</a></span>-->
<!--            </p>-->

<!--            <p>-->
<!--                <label for="id_remember">Remember Me:</label>-->
<!--                <input type="checkbox" name="remember" id="id_remember">-->
<!--            </p>-->


{% endblock content %}


{% block extra_body %}

<script type="text/javascript">
document.addEventListener('DOMContentLoaded', function () {
    const form = document.querySelector('.form-root');
    const passwordInput = form.querySelector('input[name="password"]');
    const toggleBtn = document.querySelector('.btn-toggle-visibility');

    if (toggleBtn && passwordInput) {
        toggleBtn.addEventListener('click', function () {
            var isPassword = passwordInput.type === "password";
            passwordInput.type = isPassword ? "text" : "password";
            toggleBtn.setAttribute("aria-pressed", String(isPassword));
            toggleBtn.setAttribute("title", isPassword ? "Hide password" : "Show password");
        });
    }

    // Floating label data-focused logic
    passwordInput.addEventListener('focus', function() {
        passwordInput.setAttribute('data-focused', 'true');
    });
    passwordInput.addEventListener('blur', function() {
        if (!passwordInput.value) {
            passwordInput.removeAttribute('data-focused');
        }
    });
    if (passwordInput.value) {
        passwordInput.setAttribute('data-focused', 'true');
    }

});
</script>

{% endblock extra_body %}
