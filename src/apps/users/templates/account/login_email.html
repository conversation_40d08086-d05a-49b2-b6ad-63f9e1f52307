{% extends "account/base_entrance.html" %}

{% load allauth socialaccount account i18n static %}

{% block head_title %}{% trans "Login" %}{% endblock head_title %}

{% block extra_head %}

{{ block.super }}

<!-- Canonical URL for SEO -->
<link rel="canonical" href="https://auth.wheel-size.com/login/">

{% endblock extra_head %}

{% block content %}

    <div class="title-block">
        <h1 class="heading">
            <span class="heading-main">{% trans "Welcome back" %}</span>
        </h1>
    </div>

    <fieldset class="fieldset-root">

        {% url 'custom_login_email' as login_url %}

        <form method="post"
              action="{{ login_url }}"
              id=":login:"
              class="form-root"
              autocomplete="on"
              novalidate>

            <div class="section fields">

                <div class="form-field-group">

                    {% csrf_token %}

                    {{ redirect_field }}

                    <div class="form-field {% if form.login.errors %}form-field-errors {% endif %}is-typeable">
                        <label class="form-label-absolute" for=":login:login">
                            <span class="label-text-positioner">
                                <span class="label-text">Email address</span>
                            </span>
                        </label>
                        <input id=":login:login"
                               name="login"
                               type="email"
                               class="form-input target"
                               value="{{ form.login.value|default_if_none:'' }}"
                               placeholder="Email address"
                               autocomplete="email">
                    </div>
                    <span aria-live="polite" aria-atomic="true">
                    {% if form.login.errors %}
                        <span slot="errorMessage">
                            <ul class="form-errors">
                                {% for error in form.login.errors %}
                                <li class="form_error">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" title="Error">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M8 14.667A6.667 6.667 0 1 0 8 1.333a6.667 6.667 0 0 0 0 13.334z" fill="#D00E17" stroke="#D00E17" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M8 4.583a.75.75 0 0 1 .75.75V8a.75.75 0 0 1-1.5 0V5.333a.75.75 0 0 1 .75-.75z" fill="#fff"></path>
                                        <path d="M8.667 10.667a.667.667 0 1 1-1.334 0 .667.667 0 0 1 1.334 0z" fill="#fff"></path>
                                    </svg>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        </span>
                    {% endif %}
                    </span>
                </div>

            </div>

            <div class="section ctas">
                <!-- Call-to-action buttons go here -->

                <button class="btn btn-primary"
                        type="submit"
                        name="intent"
                        value="email">Continue</button>

                <span class="_base_14rrm_63 _normal_14rrm_86 _primary_14rrm_104">Don't have an account? <a href="{{ signup_url }}">Sign up</a></span>

                <div class="section-delimiter-root">
                    <div class="section-delimiter-line"></div>
                    <div class="section-delimiter-name">Or get a one-time code</div>
                    <div class="section-delimiter-line"></div>
                </div>

                <span class="_base_14rrm_63 _normal_14rrm_86 _primary_14rrm_104"><a href="{{ request_login_code_url }}">Send me a sign-in code</a></span>

                {% if SOCIALACCOUNT_ENABLED %}

                    <div class="section-delimiter-root">
                        <div class="section-delimiter-line"></div>
                        <div class="section-delimiter-name">{% translate "Other sign-in options" %}</div>
                        <div class="section-delimiter-line"></div>
                    </div>

                    {% include "socialaccount/snippets/login.html" with page_layout="entrance" %}
                {% endif %}
            </div>

        </form>

    </fieldset>

{% endblock content %}


{% block extra_body %}

<script type="text/javascript">
document.addEventListener('DOMContentLoaded', function () {
    const form = document.querySelector('.form-root');
    const emailInput = form.querySelector('input[name="login"]');
    const params = new URLSearchParams(window.location.search);

    // Auto-focus email field if email= is in URL
    if (params.has('email') && emailInput) {
        emailInput.focus();
        emailInput.setAttribute('data-focused', 'true');
    }

    // Floating label data-focused logic
    emailInput.addEventListener('focus', function() {
        emailInput.setAttribute('data-focused', 'true');
    });
    emailInput.addEventListener('blur', function() {
        if (!emailInput.value) {
            emailInput.removeAttribute('data-focused');
        }
    });
    if (emailInput.value) {
        emailInput.setAttribute('data-focused', 'true');
    }

});
</script>

{% endblock extra_body %}
