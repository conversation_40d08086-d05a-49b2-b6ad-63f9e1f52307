import typing

from django.contrib import messages

from allauth.account.adapter import De<PERSON>ultAccountAdapter

from src.apps.users.models import User


class AccountAdapter(DefaultAccountAdapter):
    """
    Custom account adapter for handling phone-based operations and SMS verification
    in Django Allauth. Extends DefaultAccountAdapter to provide additional phone
    verification, storage, and SMS demo methods.
    """

    def set_phone(self, user, phone: str, verified: bool):
        """
        Set the phone number and verification status for a user.

        Args:
            user (User): The user instance.
            phone (str): Phone number to assign.
            verified (bool): Phone verification status.
        """
        user.phone = phone
        user.phone_verified = verified
        user.save(update_fields=["phone", "phone_verified"])

    def get_phone(self, user) -> typing.Optional[typing.Tuple[str, bool]]:
        """
        Retrieve the user's phone number and verification status.

        Args:
            user (User): The user instance.

        Returns:
            Optional[Tuple[str, bool]]: <PERSON><PERSON> (phone, phone_verified) if phone exists, else None.
        """
        if user.phone:
            return user.phone, user.phone_verified
        return None

    def set_phone_verified(self, user, phone):
        """
        Mark the given user's phone as verified.

        Args:
            user (User): The user instance.
            phone (str): The phone number to verify.
        """
        self.set_phone(user, phone, True)

    def send_verification_code_sms(self, user, phone: str, code: str, **kwargs):
        """
        Demo stub for sending SMS with a verification code.

        Args:
            user (User): The user instance.
            phone (str): Recipient phone number.
            code (str): Verification code to send.
            **kwargs: Additional arguments.
        """
        messages.add_message(
            self.request,
            messages.WARNING,
            f"⚠️ SMS demo stub: assume code {code} was sent to {phone}.",
        )

    def send_unknown_account_sms(self, phone: str, **kwargs):
        """
        Demo stub for sending SMS when account does not exist.

        Args:
            phone (str): Recipient phone number.
            **kwargs: Additional arguments.
        """
        messages.add_message(
            self.request,
            messages.WARNING,
            f"⚠️ SMS demo stub: Enumeration prevention: texted {phone} informing no account exists.",
        )

    def send_account_already_exists_sms(self, phone: str, **kwargs):
        """
        Demo stub for sending SMS when account already exists.

        Args:
            phone (str): Recipient phone number.
            **kwargs: Additional arguments.
        """
        messages.add_message(
            self.request,
            messages.WARNING,
            f"⚠️ SMS demo stub: Enumeration prevention: texted {phone} informing account already exists.",
        )

    def get_user_by_phone(self, phone):
        """
        Get a user by phone number, preferring users with verified phones.

        Args:
            phone (str): The phone number to search for.

        Returns:
            Optional[User]: User instance if found, else None.
        """
        return User.objects.filter(phone=phone).order_by("-phone_verified").first()
