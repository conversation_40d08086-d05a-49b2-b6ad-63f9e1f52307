from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.sites.shortcuts import get_current_site
from django.core.validators import validate_email
from django.forms import ValidationError
from django.shortcuts import redirect, render
from django.urls import reverse
from django.views.generic.base import Template<PERSON>iew

from allauth import app_settings as allauth_app_settings
from allauth.account.views import LoginView, SignupView

from .forms import SignupForm, LoginForm


class EmailStepSignupView(SignupView):
    """
    Handles the first step of the 2-step signup process (email entry).

    - Renders the email signup form.
    - On valid submission, checks for account existence or proceeds to the password step.
    """

    template_name = "account/signup_email.html"
    form_class = SignupForm

    def form_valid(self, form):
        """
        Processes a valid form submission.

        - Attempts to save the user or handle the case when the email already exists.
        - Stores the email in the session for the next step if account creation continues.
        - Redirects to the password entry view.

        Args:
            form (SignupForm): The validated form instance.

        Returns:
            HttpResponse: A redirect to the next signup step or a special response if the account exists.
        """
        resp = form.try_save(self.request)
        if resp:
            return resp

        email = form.cleaned_data['email']

        self.request.session['email'] = email
        return redirect(reverse('custom_signup_password'))


signup_email_step = EmailStepSignupView.as_view()


class PasswordStepSignupView(SignupView):
    template_name = "account/signup_password.html"

    def get_initial(self):
        initial = super().get_initial()
        email = self.request.session.get('email')

        if email:
            initial['email'] = email

        return initial


signup_password_step = PasswordStepSignupView.as_view()


class EmailStepLoginView(LoginView):
    template_name = "account/login_email.html"

    form_class = LoginForm

    def form_valid(self, form):
        login = form.cleaned_data['login']
        self.request.session['login'] = login
        return redirect(reverse('custom_login_password'))

    def get_initial(self):
        initial = super().get_initial()
        email = self.request.GET.get("email")
        print(email)
        if email:
            try:
                validate_email(email)
            except ValidationError:
                return initial
            initial["login"] = email

        return initial


login_email_step = EmailStepLoginView.as_view()


class PasswordStepLoginView(LoginView):
    template_name = "account/login_password.html"

    def get_initial(self):
        initial = super().get_initial()
        login = self.request.session.get('login')

        if login:
            initial['login'] = login

        return initial

login_password_step = PasswordStepLoginView.as_view()


class ProfileView(LoginRequiredMixin, TemplateView):
    template_name = "profile.html"

profile = ProfileView.as_view()
