# Pull the base image with python 3.10 as a runtime for your Lambda
FROM public.ecr.aws/lambda/python:3.10.2025.04.03.11

# Set environment variables
ENV \
  PYTHONFAULTHANDLER=1 \
  PYTHONUNBUFFERED=1 \
  PYTHONHASHSEED=random \
  PYTHONDONTWRITEBYTECODE=1 \
  PYTHONPYCACHEPREFIX='/tmp/.cache/pycache' \
  PIP_NO_CACHE_DIR=1 \
  PIP_DISABLE_PIP_VERSION_CHECK=1 \
  PIP_DEFAULT_TIMEOUT=100 \
  PIP_ROOT_USER_ACTION=ignore \
  POETRY_VERSION=1.5.1 \
  POETRY_NO_INTERACTION=1 \
  POETRY_VIRTUALENVS_CREATE=false \
  POETRY_CACHE_DIR='/tmp/.cache/pypoetry' \
  POETRY_HOME='/usr/local' \
  NUMBA_CACHE_DIR='/tmp/.cache/numba_cache' \
  MPLCONFIGDIR='/tmp/.cache/matplotlib'

# Install OS packages and other tools
RUN yum -y install findutils gcc ghostscript which make sudo tar gzip curl \
    zlib zlib-devel bzip2 bzip2-devel xz xz-devel readline-devel openjpeg2-devel \
    openssl-devel freetype-devel lcms2-devel libffi-devel libimagequant-devel \
    libjpeg-devel libraqm-devel libtiff-devel libwebp-devel libxcb-devel tcl-devel \
    tk-devel harfbuzz-devel fribidi-devel python38-devel mysql-devel postgresql-devel \
    libjpeg-turbo-devel libjpeg-turbo-utils mesa-libGL \
    && yum clean all

# Setting up the shell
SHELL ["/bin/bash", "-eo", "pipefail", "-c"]

# Installing Poetry
RUN curl -sSL https://install.python-poetry.org | python3 - \
  && poetry --version

# Set up directories
RUN mkdir -p ${NUMBA_CACHE_DIR} \
  && mkdir -p ${MPLCONFIGDIR} \
  && mkdir -p ${PYTHONPYCACHEPREFIX} \
  && mkdir -p ${POETRY_CACHE_DIR} \
  && chmod -R 777 /tmp/.cache

# Copy and install dependencies
COPY poetry.lock pyproject.toml ${LAMBDA_TASK_ROOT}
RUN --mount=type=cache,target="${POETRY_CACHE_DIR}" \
  poetry version \
  && poetry config repositories.ws https://pypi.wheel-size.com/ \
  && poetry config http-basic.ws repo-user Ojc3ZSPwIBEEisX \
  && poetry run pip install -U pip \
  && poetry install --no-interaction --no-ansi

# Install Pillow-SIMD
#RUN pip uninstall -y pillow \
#  && CC="cc -mavx2" pip install -U --force-reinstall pillow-simd

# Copy project files
COPY . ${LAMBDA_TASK_ROOT}
RUN mkdir -p ${LAMBDA_TASK_ROOT}/staticfiles \
  && chmod -R o+rX ${LAMBDA_TASK_ROOT}/deployment

# Set the CMD for the lambda handler
CMD ["app.lambda_handler"]
