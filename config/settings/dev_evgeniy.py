from .base import *


DEBUG = True


ALLOWED_HOSTS = ['auth.ws.local']


CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://redis:6379/0",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        },
        "KEY_PREFIX": "ws_auth_api"
    },
    "memcached": {
        "BACKEND": "django.core.cache.backends.memcached.PyLibMCCache",
        "LOCATION": "memcached:11211",
    },
}


DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ws-auth-1',
        'USER': 'user',
        'PASSWORD': 'password',
        'HOST': 'mysql',
        'PORT': '',
        'CONN_MAX_AGE': 350,
        'OPTIONS': {
            'init_command': 'SET default_storage_engine = InnoDB, sql_mode="TRADITIONAL"; \
                             SET SESSION group_concat_max_len = 4096;',
        },
        'COLLATION': 'utf8mb4_0900_ai_ci',
        'TIME_ZONE': 'UTC',
    }
}

EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"

ACCOUNT_EMAIL_VERIFICATION_BY_CODE_TIMEOUT = 5000
