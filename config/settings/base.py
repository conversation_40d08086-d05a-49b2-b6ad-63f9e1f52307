"""
Django settings for src project.

Generated by 'django-admin startproject' using Django 5.2.3.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

from pathlib import Path

import environ

env = environ.Env()


# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = env('DJANGO_SECRET_KEY')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = []


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.humanize',

    'allauth',                                     # Core django-allauth functionality
    'allauth.account',                             # Local user registration and account management

    'allauth.socialaccount',                       # Social account login framework
    'allauth.socialaccount.providers.google',      # Social login via Google
    'allauth.socialaccount.providers.microsoft',   # Social login via Microsoft
    'allauth.socialaccount.providers.apple',       # Social login via Apple
    'allauth.usersessions',                        # User sessions management

    'health_check',
    'health_check.db',                          # stock Django health checkers
    'health_check.cache',
    'health_check.storage',
    'health_check.contrib.migrations',
    # 'health_check.contrib.celery',              # requires celery
    # 'health_check.contrib.celery_ping',         # requires celery
    # 'health_check.contrib.psutil',              # disk and memory utilization; requires psutil
    # 'health_check.contrib.s3boto3_storage',     # requires boto3 and S3BotoStorage backend
    # 'health_check.contrib.rabbitmq',            # requires RabbitMQ broker
    # 'health_check.contrib.redis',               # requires Redis broker
    # 'health_check.contrib.db_heartbeat',
    # 'health_check.contrib.mail',

    'src.apps.users',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',

    'whitenoise.middleware.WhiteNoiseMiddleware',

    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',

    # Add the account middleware:
    "allauth.account.middleware.AccountMiddleware",
]

ROOT_URLCONF = 'config.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / "src" / "templates",],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'config.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

AUTHENTICATION_BACKENDS = [
    # Needed to login by username in Django admin, regardless of `allauth`
    'django.contrib.auth.backends.ModelBackend',

    # `allauth` specific authentication methods, such as login by email
    'allauth.account.auth_backends.AuthenticationBackend',
]

AUTH_USER_MODEL = 'users.User'  # Custom user model

# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 12,  # Set minimum password length to 12 characters
        },
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = 'static/'
STATIC_ROOT = BASE_DIR / "staticfiles"

STATICFILES_DIRS = [BASE_DIR / "static"]

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'


STORAGES = {
    "staticfiles": {
        "BACKEND": "whitenoise.storage.CompressedManifestStaticFilesStorage",
    },
}

SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")

LOGIN_URL = "/login/"
LOGIN_REDIRECT_URL = '/profile/'

ACCOUNT_LOGIN_BY_CODE_ENABLED = True               # Enable login via one-time code (OTP)
ACCOUNT_EMAIL_VERIFICATION = "mandatory"           # Require email verification for all accounts
ACCOUNT_EMAIL_VERIFICATION_BY_CODE_ENABLED = True  # Enable email verification by code (not just by link)
ACCOUNT_EMAIL_VERIFICATION_SUPPORTS_RESEND = True  # Allow 2 resends if True, or set an int for a custom limit
ACCOUNT_LOGIN_METHODS = {
    "email",  # Allow login by email (not username)
}

ACCOUNT_PASSWORD_RESET_BY_CODE_ENABLED = True      # Enable password reset via verification code
ACCOUNT_SIGNUP_FIELDS = ["email*", "password1*"]   # Fields required for signup (asterisk means required)
ACCOUNT_SIGNUP_REDIRECT_URL = '/profile/'
ACCOUNT_ADAPTER = "src.apps.users.allauth.AccountAdapter"  # Custom adapter for allauth integration

# Provider specific settings
SOCIALACCOUNT_PROVIDERS = {
    'google': {
        'APP': {
            'client_id': env('SOCIAL_GOOGLE_CLIENT_ID', default=''),
            'secret': env('SOCIAL_GOOGLE_CLIENT_SECRET', default=''),
            'key': ''
        }
    },
    'microsoft': {
        'APP': {
            'client_id': env('SOCIAL_MICROSOFT_CLIENT_ID', default=''),
            'secret': env('SOCIAL_MICROSOFT_CLIENT_SECRET', default=''),
            'key': ''
        }
    },
    'apple': {
        'APP': {
            'client_id': env('SOCIAL_APPLE_CLIENT_ID', default=''),
            'secret': env('SOCIAL_APPLE_CLIENT_SECRET', default=''),
            'key': env('SOCIAL_APPLE_KEY', default='')
        }
    }
}
