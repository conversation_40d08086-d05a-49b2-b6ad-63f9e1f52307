[tool.poetry]
name = "ws-auth-api"
version = "0.1.0"
description = ""
authors = ["<PERSON><PERSON><PERSON><PERSON><PERSON>"]
readme = "README.md"

[[tool.poetry.source]]
name = "PyPI"
priority = "primary"

[[tool.poetry.source]]
name = "ws"
url = "https://pypi.wheel-size.com/simple/"
priority = "supplemental"


[tool.poetry.dependencies]
python = "^3.10"

# Database & Cache Clients
mysqlclient = "~2.1.1"            # MySQL client for Django ORM
pylibmc = "^1.6.3"                # Memcached client for Django caching
django-redis = "^5.4.0"           # Redis cache backend for Django

# Core Framework
django = "^5.2.3"                 # Django web framework
djangorestframework = "^3.16.0"   # REST API framework for Django

# Authentication & Security
django-allauth = {extras = ["socialaccount"], version = "^65.9.0"}  # Flexible authentication, registration, social auth
django-cors-headers = "^4.7.0"    # Cross-Origin Resource Sharing headers for Django
python3-openid = "^3.2.0"         # OpenID support for Django Allauth
pyjwt = "^2.10.1"                 # JWT encoding/decoding for token-based auth
fido2 = "^2.0.0"                  # WebAuthn/FIDO2 support for passkey & MFA in allauth

# Server & Runtime
gunicorn = "^23.0.0"              # Production WSGI server for Django
serverless-wsgi = "^3.1.0"        # WSGI adapter for AWS Lambda and other serverless environments
whitenoise = {extras = ["brotli"], version = "^6.9.0"}  # Static file serving with Brotli compression support

# Operations & Monitoring
psutil = "^7.0.0"                 # System monitoring utilities (memory, CPU, etc)
sentry-sdk = "^2.30.0"            # Sentry integration for error monitoring
django-health-check = "^3.20.0"

# Configuration
django-environ = "^0.12.0"        # 12-factor environment configuration for Django



[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
